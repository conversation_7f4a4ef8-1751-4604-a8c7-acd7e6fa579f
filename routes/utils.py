from django.conf import settings
from device_manager.models import Device
from django_redis import get_redis_connection

# Get Redis connection from django-redis cache
redis_client = get_redis_connection("default")

def get_device(euid):
    # Check Redis cache for the device
    device_data = redis_client.get(f"device:{euid}")
    if device_data:
        device = Device.from_json(device_data)
        return device
    else:
        # Fallback to database query if not in cache
        device = Device.objects.filter(euid=euid).first()
        if device:
            # Cache the device data in Redis
            redis_client.set(f"device:{euid}", device.to_json(), ex=3600)  # Cache for 1 hour
        return device
