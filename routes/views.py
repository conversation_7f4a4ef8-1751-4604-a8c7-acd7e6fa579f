import json
import traceback
import logging
import os
from .utils import get_device
from django.http import HttpResponse
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.core.handlers.asgi import ASGIRequest
from django.db import transaction
from decouple import config


from device_manager.scripts import make_attr_connections
from notification_center.utils import (
    check_gateway_node,
    check_out_of_field,
    check_triggers,
    generate_global_events,
)
from device_manager.models import Device, attributes_templates
from packet_analyzer.models import DroppedPacket, UplinkPacket
from telemetry.scripts import generate_telemetries
from fields.utils import is_event_within_work_shift
from device_manager.utils.device_cache import DeviceCache
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from .async_tasks import (
    ttn_executor,
    process_telemetry_async,
    process_events_async,
    send_websocket_update_async,
    save_uplink_packet_async
)

logger = logging.getLogger("app")

# Rate limiting has been removed as requested
logger.info("TTN rate limiting: disabled")


@csrf_exempt
def ttn_app(request: ASGIRequest):
    """
    Handle TTN application webhook requests.

    This endpoint receives uplink messages from The Things Network and processes them.
    It's designed to respond quickly (202 Accepted) and process the data asynchronously.
    """
    # Only accept POST requests
    if request.method != "POST":
        return HttpResponse(status=405, content="Method not allowed")

    # Wrap the entire function in a try-except to catch any connection issues
    try:
        # Log request information for debugging
        logger.info(f"TTN request received: Content-Length: {request.headers.get('Content-Length', 'unknown')}")

        # Parse JSON payload with better error handling
        try:
            # Limit payload size to prevent memory issues
            max_size = 1024 * 1024  # 1MB
            content_length = int(request.headers.get('Content-Length', 0))

            if content_length > max_size:
                logger.warning(f"TTN payload too large: {content_length} bytes")
                return HttpResponse(status=413, content="Payload too large")

            payload = json.loads(request.body.decode("utf-8"))

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in TTN packet: {e}")
            _process_drop_packet(request.body.decode("utf-8"), f"JSON decode error: {str(e)}")
            return HttpResponse(status=400, content="Invalid JSON format")

        except UnicodeDecodeError as e:
            logger.error(f"Unicode decode error in TTN packet: {e}")
            _process_drop_packet(str(request.body), f"Unicode decode error: {str(e)}")
            return HttpResponse(status=400, content="Invalid encoding")

        except Exception as e:
            logger.error(f"Error loading TTN packet: {e}")
            logger.error(f"Request body (truncated): {str(request.body)[:200]}...")
            _process_drop_packet(str(request.body)[:1000], f"Payload parsing error: {str(e)}")
            return HttpResponse(status=400, content="Invalid payload")

        # Check API key with more detailed logging
        if "Authorization" in request.headers:
            api_key = request.headers["Authorization"]
            if api_key != settings.TTN_API_KEY:
                message = "Invalid API key in request headers"
                logger.error(f"{message}: {api_key[:10]}...")
                _process_drop_packet(payload, message)
                return HttpResponse(status=401, content=message)
        else:
            message = "No API key in request headers"
            logger.error(message)
            _process_drop_packet(payload, message)
            return HttpResponse(status=401, content=message)

        # Process the payload with improved error handling
        try:
            # Validate payload structure
            if "end_device_ids" not in payload:
                message = "Missing 'end_device_ids' in payload"
                logger.error(message)
                _process_drop_packet(payload, message)
                return HttpResponse(status=400, content=message)

            # Extract device EUI and look up device
            euid = payload["end_device_ids"]["dev_eui"]
            logger.info(f"Processing TTN message for device EUI: {euid}")

            device = get_device(euid)
            if not device:
                message = f"Device with EUI '{euid}' does not exist"
                logger.error(message)
                _process_drop_packet(payload, message)
                return HttpResponse(status=404, content="Device not found")

            # Process uplink message if present
            if "uplink_message" in payload:
                # Submit to thread pool for async processing
                ttn_executor.submit(_process_uplink, device, payload)

                # Log submission success
                logger.info(f"Submitted uplink processing task for device {device.id} ({device.name})")
            else:
                logger.info(f"No uplink_message in payload for device {device.id} ({device.name})")

            # Return success immediately without waiting for processing to complete
            return HttpResponse(status=202, content="Accepted")

        except Exception as e:
            # Log detailed error information
            logger.error(f"Error processing TTN payload: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Save the dropped packet
            try:
                dropped_packet = _process_drop_packet(payload, str(e))
                message = f"Error processing TTN packet (dropped packet id. {dropped_packet.id}): {str(e)}"
            except Exception as inner_e:
                message = f"Error processing TTN packet and failed to save dropped packet: {str(e)}, {str(inner_e)}"

            logger.error(message)
            return HttpResponse(status=500, content="Internal server error")

    except ConnectionError as e:
        # Handle connection errors specifically
        logger.error(f"Connection error in TTN app endpoint: {str(e)}")
        return HttpResponse(status=503, content="Service temporarily unavailable")

    except Exception as e:
        # Catch any other unexpected errors
        logger.error(f"Unexpected error in TTN app endpoint: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return HttpResponse(status=500, content="Internal server error")

# Rate limiting function has been removed

def _process_drop_packet(payload, exception: str = None):
    """
    Process and save a dropped packet.

    Args:
        payload: The payload that failed processing (can be dict, str, or bytes)
        exception: Optional exception message

    Returns:
        DroppedPacket: The created DroppedPacket object
    """
    devi = None
    gate = None

    # Convert payload to a format that can be stored in JSONField
    if isinstance(payload, bytes):
        try:
            # Try to decode as UTF-8 and parse as JSON
            payload_str = payload.decode('utf-8')
            try:
                payload_data = json.loads(payload_str)
            except json.JSONDecodeError:
                # Not valid JSON, store as string
                payload_data = payload_str
        except UnicodeDecodeError:
            # Not valid UTF-8, store as base64
            import base64
            payload_data = {"_base64_encoded": base64.b64encode(payload).decode('ascii')}
    elif isinstance(payload, str):
        try:
            # Try to parse as JSON
            payload_data = json.loads(payload)
        except json.JSONDecodeError:
            # Not valid JSON, store as string
            payload_data = payload
    else:
        # Assume it's already a dict or other JSON-serializable object
        payload_data = payload

    # Extract device and gateway info if available
    try:
        if isinstance(payload_data, dict) and "end_device_ids" in payload_data:
            euid = payload_data["end_device_ids"].get("dev_eui")
            if euid and Device.objects.filter(euid=euid).exists():
                devi = Device.objects.get(euid=euid)

            if "uplink_message" in payload_data:
                rx_metadata = payload_data["uplink_message"].get("rx_metadata", [])
                if rx_metadata and len(rx_metadata) > 0:
                    gateway_euid = rx_metadata[0]["gateway_ids"].get("eui")
                    if gateway_euid and Device.objects.filter(euid=gateway_euid).exists():
                        gate = Device.objects.get(euid=gateway_euid)
    except Exception as e:
        logger.error(f"Error extracting device info from dropped packet: {e}")
        # Continue with saving the packet even if we can't extract device info

    # Use provided exception or get traceback
    exception_message = exception if exception else traceback.format_exc()

    try:
        # Create DroppedPacket with devi and gate if they exist
        dropped_packet = DroppedPacket.objects.create(
            devi=devi,
            gate=gate,
            data=payload_data,
            expt=exception_message,
            rxat=timezone.now(),
        )
        return dropped_packet
    except Exception as e:
        # Last resort error handling
        logger.error(f"Failed to save dropped packet: {e}")
        logger.error(f"Exception message: {exception_message}")

        # Try to create a minimal record
        try:
            return DroppedPacket.objects.create(
                data={"error": "Failed to save original payload"},
                expt=f"Original exception: {exception_message}\nSave error: {str(e)}",
                rxat=timezone.now(),
            )
        except Exception as final_e:
            logger.error(f"Critical failure saving dropped packet: {final_e}")
            # Return a dummy object to prevent further errors
            dummy = DroppedPacket(id=0)
            dummy.expt = f"Failed to save: {str(final_e)}"
            return dummy

def _process_uplink(device: Device, payload: dict):
    """
    Process an uplink message from a device.
    This function is designed to be run in a background thread.

    The processing flow is:
    1. Extract metadata and update device attributes
    2. Set device status to Online
    3. Check if device was previously offline (using cached status)
    4. Save device to database
    5. Process events, telemetry, and other async tasks
    """
    try:
        # Get device metadata
        uplink_pkt = payload["uplink_message"]
        rx_metadata = uplink_pkt["rx_metadata"][0]
        gateway_euid = rx_metadata["gateway_ids"]["eui"]

        gateway = get_device(gateway_euid)
        if not gateway:
            message = f"Gateway with EUI '{gateway_euid}' does not exist"
            logger.error(message)
            _process_drop_packet(payload, message)
            return

        # Update device attributes
        device.attr["client"].update({
            "Frame Counter": uplink_pkt.get("f_cnt", 0),
            "RSSI": rx_metadata.get("rssi", 0),
            "SNR": rx_metadata.get("snr", 0),
        })

        # Update last updated times
        device.lupd = rx_metadata["time"]
        gateway.lupd = rx_metadata["received_at"]

        # Check if received outside work hours
        outside_work_hours = not is_event_within_work_shift(device.fild, rx_metadata["time"])

        # Process decoded payload
        if "decoded_payload" in uplink_pkt:
            messages = uplink_pkt["decoded_payload"].get("messages", [])
            for message in messages:
                for measurement in message:
                    if measurement["type"] == "Positioning Status":
                        device.attr["client"]["Positioning Status"] = measurement["measurementValue"]["statusName"]
                    elif measurement["type"] == "Event Status":
                        events = [event["eventName"] for event in measurement["measurementValue"]]
                        for event in events:
                            device.attr["client"][event] = True
                        # Reset events not in the list
                        for key in list(device.attr["client"]):
                            if "event" in key and key not in events:
                                device.attr["client"][key] = False
                    else:
                        device.attr["client"][measurement["type"]] = measurement["measurementValue"]

            # Submit event processing to thread pool
            ttn_executor.submit(
                process_events_async,
                device,
                gateway,
                outside_work_hours
            )

            # Submit telemetry processing to thread pool
            if device.type in attributes_templates and "tels" in attributes_templates[device.type]:
                ttn_executor.submit(
                    process_telemetry_async,
                    device,
                    attributes_templates[device.type]["tels"]
                )

        # Critical updates that need to happen quickly
        # First, store the current status before changing it
        old_device_status = device.stat
        old_gateway_status = gateway.stat

        # Update device & gateway status
        device.stat = "Online"
        gateway.stat = "Online"

        # Only create "back online" events if the device was previously offline
        if old_device_status == "Offline" or old_gateway_status == "Offline":
            from notification_center.models import Event
            events = []
            if old_device_status == "Offline":
                events.append(Event(
                    devi=device,
                    type="Update",
                    desc="is back online.",
                ))
            if old_gateway_status == "Offline":
                events.append(Event(
                    devi=gateway,
                    type="Update",
                    desc="is back online.",
                ))
            # Save the events directly
            for event in events:
                event.save()

        # Update devices
        device.save()
        gateway.save()


        # Submit non-critical tasks to thread pool
        # Save uplink packet asynchronously
        ttn_executor.submit(
            save_uplink_packet_async,
            device,
            gateway,
            uplink_pkt,
            rx_metadata
        )

        logger.info("'%s' sent an update.", device.name)
    except Exception as e:
        logger.error(f"Error processing uplink for device {device.id}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        _process_drop_packet(payload, str(e))